<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LanguageGems - Create Smart Assignment</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gem-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        /* Base padding for all gem-cards, aggressively tight */
        .gem-card { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); padding: 0.875rem; /* p-3.5 (14px) */ }
        .step-active { background: linear-gradient(135deg, #4f46e5, #7c3aed); }
        .step-completed { background: linear-gradient(135deg, #059669, #0d9488); }
        .step-inactive { background: #e5e7eb; }
        .game-card-selected { border: 2px solid #4f46e5; background: #eef2ff; }
        
        /* Consistent font sizes */
        .text-base { font-size: 1rem; line-height: 1.5rem; }
        .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
        .text-xs { font-size: 0.875rem; line-height: 1.25rem; } /* Made same as text-sm */

        /* Extreme vertical compression (retained for fit) */
        main { padding-top: 0.75rem; padding-bottom: 0.75rem; } /* py-3 */
        .grid > .gap-6 { gap: 1rem; } /* gap-4 (16px) */
        .space-y-4 > *:not(:last-child) { margin-bottom: 0.625rem; } /* Adjusted from 0.75rem */
        .space-y-3 > *:not(:last-child) { margin-bottom: 0.375rem; } /* Adjusted from 0.5rem */
        .space-y-2 > *:not(:last-child) { margin-bottom: 0.25rem; } /* As tight as possible */
    </style>
</head>
<body class="bg-gradient-to-br from-indigo-500 via-purple-600 to-purple-700 min-h-screen">
    <header class="bg-indigo-800 text-white py-2.5 px-6 shadow-lg">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                    <i data-lucide="gem" class="h-4 w-4 text-white"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold">Create Smart Assignment</h1>
                    <p class="text-indigo-200 text-sm">Year 9 Spanish - Class A</p>
                </div>
            </div>
            <div class="flex items-center space-x-2.5">
                <button class="bg-gray-600 hover:bg-gray-700 px-3 py-1.5 rounded-lg text-sm">
                    Save Draft
                </button>
                <button class="bg-green-600 hover:bg-green-700 px-3 py-1.5 rounded-lg text-sm">
                    Create Assignment
                </button>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-6 py-3">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-2.5">
                <div class="step-completed w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                <div class="w-10 h-1 step-completed rounded"></div>
                <div class="step-completed w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                <div class="w-10 h-1 step-completed rounded"></div>
                <div class="step-active w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                <div class="w-10 h-1 step-inactive rounded"></div>
                <div class="step-inactive w-8 h-8 rounded-full flex items-center justify-center text-gray-600 font-bold text-sm">4</div>
            </div>
            <div class="text-white text-sm">
                <span class="font-medium">Step 3 of 4:</span> Configure Games & Settings
            </div>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div class="space-y-4">
                <div class="gem-card rounded-xl shadow-lg border border-white/20">
                    <h2 class="text-lg font-bold text-gray-900 mb-2 flex items-center">
                        <i data-lucide="edit-3" class="h-5 w-5 text-blue-500 mr-1.5"></i>
                        Assignment Details
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Assignment Title</label>
                            <input type="text" value="Spanish Food & Restaurant Vocabulary" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                            <input type="date" value="2024-12-15" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                        </div>
                    </div>
                    <div class="mt-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" rows="2">Practice food vocabulary and restaurant phrases through interactive games. Focus on menu items, ordering, and dining expressions.</textarea>
                    </div>
                </div>

                <div class="gem-card rounded-xl shadow-lg border border-white/20">
                    <h2 class="text-lg font-bold text-gray-900 mb-2 flex items-center">
                        <i data-lucide="book-open" class="h-5 w-5 text-green-500 mr-1.5"></i>
                        Vocabulary Selection
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-2.5 mb-3.5">
                        <div class="border-2 border-blue-500 bg-blue-50 rounded-lg p-3 cursor-pointer">
                            <div class="flex items-center mb-1">
                                <i data-lucide="check-circle" class="h-5 w-5 text-blue-500 mr-1.5"></i>
                                <h3 class="font-semibold text-blue-800 text-base">Theme-Based</h3>
                            </div>
                            <p class="text-blue-700 text-sm">Select from curriculum themes</p>
                        </div>
                        
                        <div class="border border-gray-300 bg-white rounded-lg p-3 cursor-pointer hover:border-gray-400">
                            <div class="flex items-center mb-1">
                                <i data-lucide="circle" class="h-5 w-5 text-gray-400 mr-1.5"></i>
                                <h3 class="font-semibold text-gray-700 text-base">Custom Upload</h3>
                            </div>
                            <p class="text-gray-600 text-sm">Upload your own word list</p>
                        </div>
                        
                        <div class="border border-gray-300 bg-white rounded-lg p-3 cursor-pointer hover:border-gray-400">
                            <div class="flex items-center mb-1">
                                <i data-lucide="circle" class="h-5 w-5 text-gray-400 mr-1.5"></i>
                                <h3 class="font-semibold text-gray-700 text-base">Manual Selection</h3>
                            </div>
                            <p class="text-gray-600 text-sm">Pick individual words</p>
                        </div>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <h4 class="font-semibold text-blue-800 text-base mb-1.5">Selected: Food & Drink Theme</h4>
                        <div class="grid grid-cols-2 gap-2.5 text-sm">
                            <div>
                                <p class="text-blue-700"><strong>Subcategories:</strong></p>
                                <ul class="text-blue-600 mt-0.5 space-y-0.5">
                                    <li>• Restaurant vocabulary (15 words)</li>
                                    <li>• Food items (22 words)</li>
                                    <li>• Drinks & beverages (12 words)</li>
                                </ul>
                            </div>
                            <div>
                                <p class="text-blue-700"><strong>Difficulty Level:</strong> Foundation</p>
                                <p class="text-blue-700"><strong>Total Words:</strong> 49</p>
                                <p class="text-blue-700"><strong>Estimated Time:</strong> 25-30 minutes</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <div class="gem-card rounded-xl shadow-lg border border-white/20">
                    <h2 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                        <i data-lucide="clipboard-list" class="h-5 w-5 text-green-500 mr-1.5"></i>
                        Assignment Summary
                    </h2>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Students:</span>
                            <span class="font-medium">28 students</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Vocabulary Words:</span>
                            <span class="font-medium">49 words</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Games Selected:</span>
                            <span class="font-medium">2 games</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Estimated Time:</span>
                            <span class="font-medium">15-18 min</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Auto-Grading:</span>
                            <span class="font-medium text-green-600">Enabled</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Due Date:</span>
                            <span class="font-medium">Dec 15, 2024</span>
                        </div>
                    </div>

                    <div class="mt-3 pt-3 border-t border-gray-200">
                        <h3 class="font-semibold text-lg text-gray-900 mb-1.5">Time Savings</h3>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                            <p class="text-green-800 text-base font-medium">⏱️ Estimated time saved: 3.5 hours</p>
                            <p class="text-green-700 text-sm mt-0.5">vs. manual creation and grading</p>
                        </div>
                    </div>
                </div>

                <div class="gem-card rounded-xl shadow-lg border border-white/20">
                    <h2 class="text-lg font-bold text-gray-900 mb-2 flex items-center">
                        <i data-lucide="gamepad-2" class="h-5 w-5 text-purple-500 mr-1.5"></i>
                        Select Games (Choose up to 3)
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div class="game-card-selected rounded-lg p-3 cursor-pointer">
                            <div class="flex items-center justify-between mb-1.5">
                                <h3 class="font-semibold text-blue-800 text-base">Gem Collector</h3>
                                <i data-lucide="check-circle" class="h-5 w-5 text-blue-500"></i>
                            </div>
                            <p class="text-blue-700 text-sm mb-1.5">Match vocabulary with translations</p>
                            <div class="flex items-center text-sm text-blue-600">
                                <i data-lucide="clock" class="h-4 w-4 mr-1"></i>
                                <span>8-10 min</span>
                                <span class="mx-1">•</span>
                                <i data-lucide="star" class="h-4 w-4 mr-1"></i>
                                <span>Vocabulary Focus</span>
                            </div>
                        </div>

                        <div class="game-card-selected rounded-lg p-3 cursor-pointer">
                            <div class="flex items-center justify-between mb-1.5">
                                <h3 class="font-semibold text-blue-800 text-base">Memory Match</h3>
                                <i data-lucide="check-circle" class="h-5 w-5 text-blue-500"></i>
                            </div>
                            <p class="text-blue-700 text-sm mb-1.5">Find matching word pairs</p>
                            <div class="flex items-center text-sm text-blue-600">
                                <i data-lucide="clock" class="h-4 w-4 mr-1"></i>
                                <span>6-8 min</span>
                                <span class="mx-1">•</span>
                                <i data-lucide="brain" class="h-4 w-4 mr-1"></i>
                                <span>Memory Training</span>
                            </div>
                        </div>

                        <div class="border border-gray-300 bg-white rounded-lg p-3 cursor-pointer hover:border-gray-400">
                            <div class="flex items-center justify-between mb-1.5">
                                <h3 class="font-semibold text-gray-700 text-base">Word Blast</h3>
                                <i data-lucide="plus-circle" class="h-5 w-5 text-gray-400"></i>
                            </div>
                            <p class="text-gray-600 text-sm mb-1.5">Fast-paced vocabulary shooting</p>
                            <div class="flex items-center text-sm text-gray-500">
                                <i data-lucide="clock" class="h-4 w-4 mr-1"></i>
                                <span>10-12 min</span>
                                <span class="mx-1">•</span>
                                <i data-lucide="zap" class="h-4 w-4 mr-1"></i>
                                <span>Speed & Accuracy</span>
                            </div>
                        </div>

                        <div class="border border-gray-300 bg-white rounded-lg p-3 cursor-pointer hover:border-gray-400">
                            <div class="flex items-center justify-between mb-1.5">
                                <h3 class="font-semibold text-gray-700 text-base">Sentence Builder</h3>
                                <i data-lucide="plus-circle" class="h-5 w-5 text-gray-400"></i>
                            </div>
                            <p class="text-gray-600 text-sm mb-1.5">Construct sentences with vocabulary</p>
                            <div class="flex items-center text-sm text-gray-500">
                                <i data-lucide="clock" class="h-4 w-4 mr-1"></i>
                                <span>12-15 min</span>
                                <span class="mx-1">•</span>
                                <i data-lucide="layers" class="h-4 w-4 mr-1"></i>
                                <span>Grammar Practice</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>
</html>