<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LanguageGems - Student Progress & Insights</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        .gem-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .gem-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.25rem; /* Base padding, will be slightly reduced in specific cards below */
        }
        .risk-alert { animation: pulse 2s infinite; }
        .progress-line { stroke-dasharray: 300; stroke-dashoffset: 300; animation: drawLine 2s ease-in-out forwards; }
        @keyframes drawLine { to { stroke-dashoffset: 0; } }
        .gem-sparkle { animation: sparkle 1.5s ease-in-out infinite; }
        @keyframes sparkle { 0%, 100% { opacity: 0.5; transform: scale(1); } 50% { opacity: 1; transform: scale(1.1); } }

        /* Custom adjustments for vertical compression (re-tuned) */
        main {
            padding-top: 1rem; /* Slightly reduced top/bottom padding */
            padding-bottom: 1rem;
        }
        /* More compact margin between main grid sections */
        .grid > .mb-6 { /* Targeting the mb-8 sections to reduce to mb-6 */
            margin-bottom: 1.5rem; /* Equivalent to mb-6 */
        }
        /* Tighter space-y defaults for inner components */
        .space-y-4 > *:not(:last-child) {
            margin-bottom: 0.75rem; /* Reduced from 1rem */
        }
        .space-y-3 > *:not(:last-child) {
            margin-bottom: 0.5rem;
        }
        .space-y-2 > *:not(:last-child) {
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-indigo-500 via-purple-600 to-purple-700 min-h-screen">
    <header class="bg-indigo-800 text-white py-3 px-6 shadow-lg"> <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-2.5"> <button class="text-indigo-200 hover:text-white">
                    <i data-lucide="arrow-left" class="h-5 w-5"></i> </button>
                <div class="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center"> <i data-lucide="gem" class="h-5 w-5 text-white"></i> </div>
                <div>
                    <h1 class="text-xl font-bold">Student Progress Profile</h1> <p class="text-indigo-200 text-xs">Sarah Davies • Year 9 Spanish</p> </div>
            </div>
            <div class="flex items-center space-x-3"> <button class="bg-indigo-600 hover:bg-indigo-700 px-3 py-1.5 rounded-lg flex items-center space-x-1.5"> <i data-lucide="message-circle" class="h-3.5 w-3.5"></i> <span class="text-sm">Contact Parent</span>
                </button>
                <button class="bg-purple-600 hover:bg-purple-700 px-3 py-1.5 rounded-lg flex items-center space-x-1.5"> <i data-lucide="download" class="h-3.5 w-3.5"></i> <span class="text-sm">Export Report</span>
                </button>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-6 py-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
            <div class="gem-card rounded-xl p-4 shadow-lg border border-white/20"> <div class="text-center">
                    <h2 class="text-lg font-bold text-gray-900 mb-1">Sarah Davies</h2> <p class="text-gray-600 text-sm">Year 9 • Spanish</p>
                    <div class="mt-3 space-y-1"> <div class="flex items-center justify-center space-x-1.5">
                            <i data-lucide="calendar" class="h-4 w-4 text-gray-500"></i>
                            <span class="text-sm text-gray-600">Joined Sept 2024</span>
                        </div>
                        <div class="flex items-center justify-center space-x-1.5">
                            <i data-lucide="clock" class="h-4 w-4 text-gray-500"></i>
                            <span class="text-sm text-gray-600">Last active: 2 hours ago</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="gem-card rounded-xl p-4 shadow-lg border border-white/20"> <div class="flex items-center justify-between mb-3">
                    <h3 class="font-semibold text-gray-900 text-base">Current Performance</h3>
                    <i data-lucide="trending-down" class="h-5 w-5 text-red-500"></i>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-red-600 mb-1">64%</div>
                    <p class="text-red-600 text-sm font-medium">Below Class Average (78%)</p>
                    <div class="mt-3 w-full bg-gray-200 rounded-full h-2.5">
                        <div class="bg-red-500 h-2.5 rounded-full" style="width: 64%"></div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2 gem-card rounded-xl p-4 shadow-lg border border-red-200 bg-red-50 risk-alert"> <div class="flex items-start space-x-3">
                    <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <i data-lucide="alert-triangle" class="h-5 w-5 text-white"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-lg font-bold text-red-800 mb-1.5">⚠️ Intervention Recommended</h3>
                        <p class="text-red-700 text-sm mb-2">AI analysis predicts Sarah is at risk of falling behind. Performance has declined 18% over the past 3 weeks.</p>
                        <div class="flex flex-wrap gap-1.5">
                            <span class="bg-red-200 text-red-800 px-2.5 py-0.5 rounded-full text-sm font-medium">High Risk</span>
                            <span class="bg-orange-200 text-orange-800 px-2.5 py-0.5 rounded-full text-sm font-medium">Declining Trend</span>
                            <span class="bg-yellow-200 text-yellow-800 px-2.5 py-0.5 rounded-full text-sm font-medium">Low Engagement</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
            <div class="gem-card rounded-xl p-5 shadow-lg border border-white/20">
                <h3 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                    <i data-lucide="line-chart" class="h-5 w-5 text-blue-500 mr-2"></i>
                    Performance Trend (Last 8 Weeks)
                </h3>
                <div class="h-52">
                    <canvas id="performanceChart"></canvas>
                </div>
                </div>

            <div class="gem-card rounded-xl p-5 shadow-lg border border-white/20">
                <h3 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                    <i data-lucide="bar-chart-3" class="h-5 w-5 text-purple-500 mr-2"></i>
                    Skill Area Performance
                </h3>
                <div class="space-y-3">
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700">Vocabulary Retention</span>
                            <span class="text-sm font-bold text-green-600">82%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 82%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700">Grammar Mastery</span>
                            <span class="text-sm font-bold text-red-600">45%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-red-500 h-2 rounded-full" style="width: 45%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700">Listening Comprehension</span>
                            <span class="text-sm font-bold text-yellow-600">71%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 71%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700">Reading Comprehension</span>
                            <span class="text-sm font-bold text-blue-600">78%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 78%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div class="gem-card rounded-xl p-5 shadow-lg border border-white/20">
                <h3 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                    <i data-lucide="target" class="h-5 w-5 text-red-500 mr-2"></i>
                    Specific Areas Needing Focus
                </h3>
                
                <div class="space-y-3">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-3.5">
                        <h4 class="font-semibold text-red-800 text-base mb-1.5">Difficult Vocabulary</h4>
                        <div class="flex flex-wrap gap-1.5 mb-1.5">
                            <span class="bg-red-200 text-red-800 px-2.5 py-1 rounded text-sm">subir (to go up)</span>
                            <span class="bg-red-200 text-red-800 px-2.5 py-1 rounded text-sm">bajar (to go down)</span>
                            <span class="bg-red-200 text-red-800 px-2.5 py-1 rounded text-sm">conducir (to drive)</span>
                        </div>
                        <p class="text-red-700 text-sm">Accuracy: 32% • Attempts: 47</p>
                    </div>

                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-3.5">
                        <h4 class="font-semibold text-orange-800 text-base mb-1.5">Grammar Challenge</h4>
                        <p class="text-orange-800 font-medium text-sm">Subjunctive Mood</p>
                        <p class="text-orange-700 text-sm mt-1">Struggles with "que + subjunctive" constructions</p>
                        <p class="text-orange-700 text-sm">Accuracy: 28% • Common errors: using indicative instead</p>
                    </div>

                    </div>
            </div>

            <div class="gem-card rounded-xl p-5 shadow-lg border border-white/20">
                <h3 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                    <i data-lucide="brain" class="h-5 w-5 text-purple-500 mr-2"></i>
                    AI-Powered Recommendations
                </h3>

                <div class="space-y-3">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3.5">
                        <h4 class="font-semibold text-blue-800 text-base mb-2 flex items-center">
                            <i data-lucide="zap" class="h-4 w-4 mr-1"></i>
                            Immediate Actions
                        </h4>
                        <ul class="space-y-1.5 text-sm">
                            <li class="flex items-start">
                                <i data-lucide="play" class="h-4 w-4 text-blue-600 mr-2 mt-0.5"></i>
                                <span class="text-blue-800"><strong>Verb Conjugation Ladder:</strong> Focus on subjunctive practice (15 min/day)</span>
                            </li>
                            <li class="flex items-start">
                                <i data-lucide="book-open" class="h-4 w-4 text-blue-600 mr-2 mt-0.5"></i>
                                <span class="text-blue-800"><strong>Vocabulary Review:</strong> Spaced repetition for movement verbs</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-green-50 border border-green-200 rounded-lg p-3.5">
                        <h4 class="font-semibold text-green-800 text-base mb-2 flex items-center">
                            <i data-lucide="clipboard-list" class="h-4 w-4 mr-1"></i>
                            Suggested Assignments
                        </h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <span class="text-green-800 text-sm font-medium">Grammar Focus: Subjunctive Basics</span>
                                <button class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs">
                                    Create
                                </button>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-green-800 text-sm font-medium">Vocabulary: Movement Verbs Review</span>
                                <button class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs">
                                    Create
                                </button>
                            </div>
                            </div>
                    </div>

                    </div>
            </div>
        </div>
    </main>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Create performance chart
        const ctx = document.getElementById('performanceChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['W1', 'W2', 'W3', 'W4', 'W5', 'W6', 'W7', 'W8'],
                datasets: [{
                    label: 'Performance %',
                    data: [75, 82, 89, 85, 78, 72, 68, 64],
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 50,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 0,
                            minRotation: 0
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>