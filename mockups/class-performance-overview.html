<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LanguageGems - Class Performance Overview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body { font-family: 'Inter', sans-serif; }
        /* Adjusted gem-card padding/margin for compactness */
        .gem-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.25rem; /* Reduced from p-6 */
        }
        .gem-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .status-struggling { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
        .status-ontrack { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
        .status-mastered { background: linear-gradient(135deg, #45b7d1, #96c93d); }
        .weak-area-bar { transition: width 0.8s ease-in-out; }

        /* Custom adjustments for smaller screen heights if needed, not directly in this code
           but important for screenshot testing. */
        @media (max-height: 900px) { /* Adjust this value based on common screenshot resolutions */
            .gem-card {
                padding: 1rem; /* Even smaller padding for very short screens */
            }
            main {
                padding-top: 1rem;
                padding-bottom: 1rem;
            }
            .space-y-4 > *:not(:last-child) {
                margin-bottom: 0.75rem; /* Reduce default space-y */
            }
            .space-y-3 > *:not(:last-child) {
                margin-bottom: 0.5rem; /* Reduce default space-y */
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-indigo-500 via-purple-600 to-purple-700 min-h-screen">
    <header class="bg-indigo-800 text-white py-4 px-6 shadow-lg">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center">
                    <i data-lucide="gem" class="h-6 w-6 text-white"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold">LanguageGems Dashboard</h1>
                    <p class="text-indigo-200 text-sm">Year 9 Spanish - Class A</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <button class="bg-indigo-600 hover:bg-indigo-700 px-4 py-2 rounded-lg flex items-center space-x-2">
                    <i data-lucide="download" class="h-4 w-4"></i>
                    <span>Export Report</span>
                </button>
                <button class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg flex items-center space-x-2">
                    <i data-lucide="refresh-cw" class="h-4 w-4"></i>
                    <span>Refresh</span>
                </button>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-6 py-6"> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"> <div class="gem-card rounded-xl shadow-lg border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Class Average Score</p>
                        <p class="text-3xl font-bold text-gray-900">78%</p>
                        <div class="flex items-center mt-1"> <span class="text-green-600 text-sm font-medium">+5.2% this week</span> </div>
                    </div>
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <i data-lucide="trophy" class="h-6 w-6 text-white"></i> </div>
                </div>
            </div>

            <div class="gem-card rounded-xl shadow-lg border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Total Students</p>
                        <p class="text-3xl font-bold text-gray-900">28</p>
                        <div class="flex items-center mt-1">
                            <span class="text-blue-600 text-sm font-medium">24 active today</span> </div>
                    </div>
                    <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center">
                        <i data-lucide="users" class="h-6 w-6 text-white"></i> </div>
                </div>
            </div>

            <div class="gem-card rounded-xl shadow-lg border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Assignment Completion</p>
                        <p class="text-3xl font-bold text-gray-900">85%</p>
                        <div class="flex items-center mt-1">
                            <span class="text-green-600 text-sm font-medium">Above target</span> </div>
                    </div>
                    <div class="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center">
                        <i data-lucide="target" class="h-6 w-6 text-white"></i> </div>
                </div>
            </div>

            <div class="gem-card rounded-xl shadow-lg border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm font-medium">Weekly Progress</p>
                        <p class="text-3xl font-bold text-gray-900">+12%</p>
                        <div class="flex items-center mt-1">
                            <span class="text-purple-600 text-sm font-medium">Excellent momentum</span> </div>
                    </div>
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <i data-lucide="trending-up" class="h-6 w-6 text-white"></i> </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2 gem-card rounded-xl shadow-lg border border-white/20">
                <div class="flex items-center justify-between mb-4"> <h2 class="text-xl font-bold text-gray-900 flex items-center">
                        <i data-lucide="alert-circle" class="h-5 w-5 text-orange-500 mr-2"></i>
                        Class Weak Areas - Needs Attention
                    </h2>
                    <button class="inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg shadow-sm">
                        <span>View Details</span>
                        <i data-lucide="arrow-right" class="h-4 w-4 ml-1"></i>
                    </button>
                </div>
                
                <div class="space-y-3"> <div class="bg-red-50 border border-red-200 rounded-lg p-3"> <div class="flex items-center justify-between mb-1"> <h3 class="font-semibold text-red-800 text-base">Past Tense Verbs (Preterite)</h3>
                            <span class="text-red-600 font-bold text-sm">47% accuracy</span>
                        </div>
                        <div class="w-full bg-red-200 rounded-full h-1.5 mb-1"> <div class="weak-area-bar bg-red-500 h-1.5 rounded-full" style="width: 47%"></div>
                        </div>
                        <p class="text-red-700 text-sm">18 students struggling &bull; Focus: irregular verbs (fue, hizo, dijo)</p> </div>

                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-orange-800 text-base">Food Vocabulary</h3>
                            <span class="text-orange-600 font-bold text-sm">62% accuracy</span>
                        </div>
                        <div class="w-full bg-orange-200 rounded-full h-1.5 mb-1">
                            <div class="weak-area-bar bg-orange-500 h-1.5 rounded-full" style="width: 62%"></div>
                        </div>
                        <p class="text-orange-700 text-sm">14 students struggling &bull; Focus: meat/fish terms (carne, pescado, pollo)</p>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-yellow-800 text-base">Ser vs Estar Usage</h3>
                            <span class="text-yellow-600 font-bold text-sm">71% accuracy</span>
                        </div>
                        <div class="w-full bg-yellow-200 rounded-full h-1.5 mb-1">
                            <div class="weak-area-bar bg-yellow-500 h-1.5 rounded-full" style="width: 71%"></div>
                        </div>
                        <p class="text-yellow-700 text-sm">12 students struggling &bull; Focus: permanent vs temporary states</p>
                    </div>
                </div>

                <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"> <div class="flex items-center">
                        <i data-lucide="lightbulb" class="h-4 w-4 text-blue-500 mr-2"></i>
                        <span class="text-blue-800 text-sm font-medium">Recommendation:</span> </div>
                    <p class="text-blue-700 text-sm mt-1">Create targeted assignments focusing on preterite tense practice. Consider using Sentence Towers game for verb conjugation drills.</p> </div>
            </div>

            <div class="gem-card rounded-xl shadow-lg border border-white/20">
                <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center"> <i data-lucide="users" class="h-5 w-5 text-blue-500 mr-2"></i>
                    Student Status Overview
                </h2>
                
                <div class="space-y-2"> <div class="flex items-center justify-between p-2 bg-red-50 border border-red-200 rounded-lg"> <div class="flex items-center">
                            <div class="w-2.5 h-2.5 status-struggling rounded-full mr-2"></div> <div>
                                <p class="font-medium text-gray-900 text-base">Emma Thompson</p> <p class="text-sm text-red-600">Needs Support</p> </div>
                        </div>
                        <span class="text-red-600 font-bold text-sm">52%</span> </div>

                    <div class="flex items-center justify-between p-2 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2.5 h-2.5 status-struggling rounded-full mr-2"></div>
                            <div>
                                <p class="font-medium text-gray-900 text-base">James Wilson</p>
                                <p class="text-sm text-red-600">Struggling</p>
                            </div>
                        </div>
                        <span class="text-red-600 font-bold text-sm">48%</span>
                    </div>

                    <div class="flex items-center justify-between p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2.5 h-2.5 status-ontrack rounded-full mr-2"></div>
                            <div>
                                <p class="font-medium text-gray-900 text-base">Sophie Chen</p>
                                <p class="text-sm text-yellow-600">On Track</p>
                            </div>
                        </div>
                        <span class="text-yellow-600 font-bold text-sm">74%</span>
                    </div>

                    <div class="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2.5 h-2.5 status-mastered rounded-full mr-2"></div>
                            <div>
                                <p class="font-medium text-gray-900 text-base">Alex Rodriguez</p>
                                <p class="text-sm text-green-600">Excelling</p>
                            </div>
                        </div>
                        <span class="text-green-600 font-bold text-sm">94%</span>
                    </div>

                    <div class="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-2.5 h-2.5 status-mastered rounded-full mr-2"></div>
                            <div>
                                <p class="font-medium text-gray-900 text-base">Maya Patel</p>
                                <p class="text-sm text-green-600">Mastered</p>
                            </div>
                        </div>
                        <span class="text-green-600 font-bold text-sm">91%</span>
                    </div>
                </div>

                <div class="mt-4 pt-3 border-t border-gray-200"> <div class="grid grid-cols-3 gap-3 text-center"> <div>
                            <p class="text-2xl font-bold text-red-600">6</p>
                            <p class="text-sm text-gray-600">Struggling</p> </div>
                        <div>
                            <p class="text-2xl font-bold text-yellow-600">14</p>
                            <p class="text-sm text-gray-600">On Track</p> </div>
                        <div>
                            <p class="text-2xl font-bold text-green-600">8</p>
                            <p class="text-sm text-gray-600">Excelling</p> </div>
                    </div>
                </div>

                <button class="w-full mt-3 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm font-medium">
                    View All Students
                </button>
            </div>
        </div>
    </main>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Animate progress bars on load
        window.addEventListener('load', function() {
            setTimeout(() => {
                const bars = document.querySelectorAll('.weak-area-bar');
                bars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 100);
                });
            }, 500);
        });
    </script>
</body>
</html>