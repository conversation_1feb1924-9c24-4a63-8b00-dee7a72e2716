import { PollyClient, SynthesizeSpeechCommand } from '@aws-sdk/client-polly';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Amazon Polly Configuration
export interface TTSConfig {
  languageCode: string;
  voiceId: string;
  engine: 'standard' | 'neural';
  outputFormat: 'mp3' | 'ogg_vorbis' | 'pcm';
  sampleRate: string;
  textType: 'text' | 'ssml';
}

// Voice configurations for each language
// Optimized for language learning with clear pronunciation
export const VOICE_CONFIGS: Record<string, TTSConfig> = {
  spanish: {
    languageCode: 'es-ES',
    voiceId: 'Lucia', // High-quality Spanish neural voice
    engine: 'neural',
    outputFormat: 'mp3',
    sampleRate: '22050',
    textType: 'text'
  },
  french: {
    languageCode: 'fr-FR',
    voiceId: 'Lea', // High-quality French neural voice
    engine: 'neural',
    outputFormat: 'mp3',
    sampleRate: '22050',
    textType: 'text'
  },
  german: {
    languageCode: 'de-DE',
    voiceId: 'Vicki', // High-quality German neural voice
    engine: 'neural',
    outputFormat: 'mp3',
    sampleRate: '22050',
    textType: 'text'
  }
};

// Alternative voice configurations for variety
export const ALTERNATIVE_VOICES: Record<string, TTSConfig[]> = {
  spanish: [
    {
      languageCode: 'es-ES',
      voiceId: 'Enrique', // Male Spanish voice
      engine: 'neural',
      outputFormat: 'mp3',
      sampleRate: '22050',
      textType: 'text'
    },
    {
      languageCode: 'es-US',
      voiceId: 'Lupe', // US Spanish female voice
      engine: 'neural',
      outputFormat: 'mp3',
      sampleRate: '22050',
      textType: 'text'
    }
  ],
  french: [
    {
      languageCode: 'fr-FR',
      voiceId: 'Mathieu', // Male French voice
      engine: 'neural',
      outputFormat: 'mp3',
      sampleRate: '22050',
      textType: 'text'
    },
    {
      languageCode: 'fr-CA',
      voiceId: 'Chantal', // Canadian French voice
      engine: 'standard',
      outputFormat: 'mp3',
      sampleRate: '22050',
      textType: 'text'
    }
  ],
  german: [
    {
      languageCode: 'de-DE',
      voiceId: 'Daniel', // Male German voice
      engine: 'neural',
      outputFormat: 'mp3',
      sampleRate: '22050',
      textType: 'text'
    },
    {
      languageCode: 'de-DE',
      voiceId: 'Marlene', // Alternative female German voice
      engine: 'standard',
      outputFormat: 'mp3',
      sampleRate: '22050',
      textType: 'text'
    }
  ]
};

export class AmazonPollyService {
  private client: PollyClient;
  private supabaseClient;
  private bucketName: string = 'audio';

  constructor() {
    // Check for required environment variables
    const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
    const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
    const region = process.env.AWS_REGION || 'us-east-1';

    if (!accessKeyId || !secretAccessKey) {
      throw new Error(
        'AWS credentials not found. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in your .env.local file.'
      );
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error(
        'Supabase configuration not found. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY.'
      );
    }

    this.supabaseClient = createClient(supabaseUrl, supabaseKey);

    console.log(`🔧 Initializing Amazon Polly client in region: ${region}`);
    console.log(`🔑 Using Access Key ID: ${accessKeyId.substring(0, 8)}...`);
    console.log(`📁 Uploading to Supabase bucket: ${this.bucketName}`);

    // Initialize the Polly client with credentials from environment variables
    this.client = new PollyClient({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });
  }

  /**
   * Generate audio for a single word and upload to Supabase Storage
   */
  async generateAudio(
    text: string, 
    language: string, 
    filename: string,
    customConfig?: Partial<TTSConfig>
  ): Promise<string> {
    try {
      const config = { ...VOICE_CONFIGS[language], ...customConfig };
      
      if (!config) {
        throw new Error(`Unsupported language: ${language}`);
      }

      // Construct the Polly request
      const command = new SynthesizeSpeechCommand({
        Text: text,
        VoiceId: config.voiceId as any,
        OutputFormat: config.outputFormat as any,
        SampleRate: config.sampleRate,
        Engine: config.engine as any,
        TextType: config.textType as any,
        LanguageCode: config.languageCode as any,
      });

      console.log(`🎵 Generating audio for "${text}" in ${language} using voice ${config.voiceId}...`);

      // Perform the text-to-speech request
      const response = await this.client.send(command);

      if (!response.AudioStream) {
        throw new Error('No audio content received from Amazon Polly');
      }

      // Convert the audio stream to buffer
      const audioBuffer = await this.streamToBuffer(response.AudioStream);

      // Upload to Supabase Storage
      const storagePath = `detective-listening/${filename}`;
      const { data, error } = await this.supabaseClient.storage
        .from(this.bucketName)
        .upload(storagePath, audioBuffer, {
          contentType: 'audio/mpeg',
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        throw new Error(`Failed to upload to Supabase Storage: ${error.message}`);
      }

      // Get the public URL
      const { data: urlData } = this.supabaseClient.storage
        .from(this.bucketName)
        .getPublicUrl(storagePath);

      console.log(`✅ Audio uploaded to Supabase: ${urlData.publicUrl}`);
      return urlData.publicUrl;

    } catch (error) {
      console.error(`❌ Error generating audio for "${text}":`, error);
      throw error;
    }
  }

  /**
   * Convert a readable stream to buffer
   */
  private async streamToBuffer(stream: any): Promise<Buffer> {
    const chunks: Uint8Array[] = [];
    
    for await (const chunk of stream) {
      chunks.push(chunk);
    }
    
    return Buffer.concat(chunks);
  }

  /**
   * Check if file exists in Supabase Storage
   */
  private async fileExists(storagePath: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabaseClient.storage
        .from(this.bucketName)
        .download(storagePath);
      
      return !error && !!data;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate audio for multiple words with progress tracking
   */
  async generateBulkAudio(
    words: Array<{ text: string; language: string; filename: string }>,
    onProgress?: (current: number, total: number, filename: string) => void
  ): Promise<string[]> {
    const results: string[] = [];
    const total = words.length;

    for (let i = 0; i < words.length; i++) {
      const { text, language, filename } = words[i];
      
      try {
        // Check if file already exists in Supabase Storage
        const storagePath = `detective-listening/${filename}`;
        const exists = await this.fileExists(storagePath);
        
        if (exists) {
          console.log(`⏭️  Skipping existing file: ${filename}`);
          const { data: urlData } = this.supabaseClient.storage
            .from(this.bucketName)
            .getPublicUrl(storagePath);
          results.push(urlData.publicUrl);
          onProgress?.(i + 1, total, filename);
          continue;
        }

        const result = await this.generateAudio(text, language, filename);
        results.push(result);
        
        // Progress callback
        onProgress?.(i + 1, total, filename);
        
        // Rate limiting: wait 100ms between requests to avoid hitting API limits
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`Failed to generate audio for ${filename}:`, error);
        // Continue with other files even if one fails
      }
    }

    return results;
  }

  /**
   * List available voices for a language
   */
  async listVoices(languageCode?: string): Promise<any[]> {
    try {
      // Amazon Polly doesn't have a direct listVoices API like Google
      // Instead, we'll return the configured voices for the language
      if (languageCode) {
        const language = Object.keys(VOICE_CONFIGS).find(
          lang => VOICE_CONFIGS[lang].languageCode === languageCode
        );
        
        if (language) {
          const mainVoice = VOICE_CONFIGS[language];
          const altVoices = ALTERNATIVE_VOICES[language] || [];
          return [mainVoice, ...altVoices];
        }
      }
      
      // Return all configured voices
      const allVoices = Object.values(VOICE_CONFIGS);
      const allAltVoices = Object.values(ALTERNATIVE_VOICES).flat();
      return [...allVoices, ...allAltVoices];
    } catch (error) {
      console.error('Error listing voices:', error);
      throw error;
    }
  }

  /**
   * Test the TTS service with a simple phrase
   */
  async testService(): Promise<boolean> {
    try {
      console.log('🧪 Testing Amazon Polly service...');
      
      const testUrl = await this.generateAudio(
        'Hola, esto es una prueba.',
        'spanish', // Using Spanish text for testing
        'test-audio.mp3'
      );
      
      // Check if the uploaded file is accessible
      const response = await fetch(testUrl);
      const success = response.ok && !!response.headers.get('content-type')?.includes('audio');
      
      if (success) {
        console.log('✅ Amazon Polly service is working correctly!');
        console.log(`🔗 Test file available at: ${testUrl}`);
        
        // Clean up test file
        await this.deleteFile('detective-listening/test-audio.mp3');
      } else {
        console.log('❌ Test file was uploaded but is not accessible');
      }
      
      return success;
    } catch (error) {
      console.error('❌ Amazon Polly test failed:', error);
      return false;
    }
  }

  /**
   * Get estimated cost for generating audio
   */
  calculateEstimatedCost(totalCharacters: number): number {
    // Amazon Polly pricing: $4.00 per 1 million characters for Neural voices
    // Standard voices are $4.00 per 1 million characters as well
    const pricePerMillion = 4.00;
    return (totalCharacters / 1000000) * pricePerMillion;
  }

  /**
   * Delete a file from Supabase Storage
   */
  private async deleteFile(storagePath: string): Promise<boolean> {
    try {
      const { error } = await this.supabaseClient.storage
        .from(this.bucketName)
        .remove([storagePath]);
      
      if (error) {
        console.error(`Error deleting file ${storagePath}:`, error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error(`Error deleting file ${storagePath}:`, error);
      return false;
    }
  }

  /**
   * List all files in the audio bucket
   */
  async listAudioFiles(folder: string = 'detective-listening'): Promise<any[]> {
    try {
      const { data, error } = await this.supabaseClient.storage
        .from(this.bucketName)
        .list(folder);

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error listing audio files:', error);
      return [];
    }
  }

  /**
   * Clean up old or test audio files in Supabase Storage
   */
  async cleanupAudioFiles(pattern?: string): Promise<void> {
    try {
      const files = await this.listAudioFiles('detective-listening');
      const filesToDelete = pattern 
        ? files.filter(file => file.name.includes(pattern))
        : files.filter(file => file.name.startsWith('test-'));
      
      for (const file of filesToDelete) {
        const deleted = await this.deleteFile(`detective-listening/${file.name}`);
        if (deleted) {
          console.log(`🗑️  Deleted: ${file.name}`);
        }
      }
      
      console.log(`✅ Cleaned up ${filesToDelete.length} files`);
    } catch (error) {
      console.error('Error cleaning up files:', error);
    }
  }
}
