'use client';

import { useState, useEffect } from 'react';
import { supabaseBrowser } from '../components/auth/AuthProvider';
import type { UnifiedSelectionConfig } from '../components/games/UnifiedCategorySelector';

// Re-export the config type for convenience
export type { UnifiedSelectionConfig };

export interface UnifiedVocabularyItem {
  id: string;
  word: string;
  translation: string;
  language: string;
  category: string;
  subcategory?: string;
  part_of_speech?: string;
  example_sentence_original?: string;
  example_sentence_translation?: string;
  difficulty_level?: string;
  audio_url?: string;
}

export interface UseUnifiedVocabularyOptions {
  config?: UnifiedSelectionConfig;
  limit?: number;
  randomize?: boolean;
  hasAudio?: boolean;
  customVocabulary?: UnifiedVocabularyItem[];
}

export interface UseUnifiedVocabularyReturn {
  vocabulary: UnifiedVocabularyItem[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
  isEmpty: boolean;
}

/**
 * Unified vocabulary hook that works with the new UnifiedCategorySelector
 * Handles loading vocabulary from the centralized_vocabulary table based on
 * language, curriculum level, category, and subcategory selections
 */
export function useUnifiedVocabulary({
  config,
  limit = 50,
  randomize = true,
  hasAudio = false,
  customVocabulary
}: UseUnifiedVocabularyOptions = {}): UseUnifiedVocabularyReturn {
  const [vocabulary, setVocabulary] = useState<UnifiedVocabularyItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchVocabulary = async () => {
    // Handle custom vocabulary mode
    if (config?.customMode) {
      if (customVocabulary && customVocabulary.length > 0) {
        setVocabulary(customVocabulary);
        setLoading(false);
        setError(null);
        return;
      } else {
        // Custom mode but no vocabulary provided - this is expected initially
        setVocabulary([]);
        setLoading(false);
        setError(null);
        return;
      }
    }

    // Don't fetch if no config provided
    if (!config) {
      setVocabulary([]);
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const supabase = supabaseBrowser;
      
      // Build the query
      let query = supabase
        .from('centralized_vocabulary')
        .select(`
          id,
          word,
          translation,
          language,
          part_of_speech,
          example_sentence,
          example_translation,
          category,
          subcategory,
          tier,
          curriculum_level,
          is_required,
          audio_url
        `)
        .in('language', mapLanguageCodes(config.language)); // Use helper to get all possible language codes

      // Filter by curriculum level
      if (config.curriculumLevel === 'KS3') {
        // For KS3, use curriculum_level or tier
        query = query.or('curriculum_level.eq.KS3,tier.in.(Foundation,Core)');
      } else if (config.curriculumLevel === 'KS4') {
        // For KS4, use curriculum_level or tier
        query = query.or('curriculum_level.eq.KS4,tier.in.(Foundation,Higher,Core)');
      }

      // Filter by category
      if (config.categoryId && config.categoryId !== 'custom') {
        query = query.eq('category', config.categoryId);
      }

      // Filter by subcategory if specified
      if (config.subcategoryId) {
        query = query.eq('subcategory', config.subcategoryId);
      }

      // Apply limit
      if (limit > 0) {
        query = query.limit(limit);
      }

      // Execute query
      const { data, error: queryError } = await query;

      if (queryError) {
        throw new Error(`Failed to load vocabulary: ${queryError.message}`);
      }

      if (!data || data.length === 0) {
        setError(`No vocabulary found for ${config.language} - ${config.categoryId}`);
        setVocabulary([]);
        return;
      }

      // Transform data to unified format
      let transformedVocabulary: UnifiedVocabularyItem[] = data.map(item => ({
        id: item.id,
        word: item.word,
        translation: item.translation,
        language: item.language,
        category: item.category || config.categoryId, // Use database category or fallback to config
        subcategory: item.subcategory || config.subcategoryId, // Use database subcategory or fallback to config
        part_of_speech: item.part_of_speech,
        example_sentence_original: item.example_sentence,
        example_sentence_translation: item.example_translation,
        difficulty_level: item.tier?.toLowerCase() || item.curriculum_level?.toLowerCase(),
        audio_url: item.audio_url // Use the actual audio URL from the database
      }));

      // Filter by subcategory if specified
      if (config.subcategoryId) {
        // This would require additional logic to map subcategories to specific vocabulary
        // For now, we'll use all vocabulary from the category
      }

      // Randomize if requested
      if (randomize) {
        transformedVocabulary = transformedVocabulary.sort(() => Math.random() - 0.5);
      }

      // Filter by audio requirement
      if (hasAudio) {
        transformedVocabulary = transformedVocabulary.filter(item => item.audio_url);
      }

      setVocabulary(transformedVocabulary);
      console.log(`Loaded ${transformedVocabulary.length} vocabulary items for ${config.language} - ${config.categoryId}`);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error loading vocabulary:', err);
      setVocabulary([]);
    } finally {
      setLoading(false);
    }
  };

  // Refetch function
  const refetch = () => {
    fetchVocabulary();
  };

  // Effect to load vocabulary when config changes
  useEffect(() => {
    fetchVocabulary();
  }, [
    config?.language,
    config?.curriculumLevel,
    config?.categoryId,
    config?.subcategoryId,
    config?.customMode,
    limit,
    randomize,
    hasAudio,
    customVocabulary
  ]);

  return {
    vocabulary,
    loading,
    error,
    refetch,
    isEmpty: vocabulary.length === 0 && !loading
  };
}

/**
 * Get all possible language codes for a given language (handles variants)
 */
function mapLanguageCodes(languageCode: string): string[] {
  const languageVariantsMap: Record<string, string[]> = {
    'es': ['es', 'ES', 'spanish'], // Spanish has multiple variants in the database
    'fr': ['fr', 'french'],
    'de': ['de', 'german'], 
    'en': ['en', 'english']
  };
  
  return languageVariantsMap[languageCode] || [languageCode];
}

/**
 * Helper hook for games that need to show a loading state while vocabulary loads
 */
export function useVocabularyWithLoadingGate(
  config?: UnifiedSelectionConfig,
  options?: Omit<UseUnifiedVocabularyOptions, 'config'>
) {
  const { vocabulary, loading, error, refetch, isEmpty } = useUnifiedVocabulary({
    config,
    ...options
  });

  const canStartGame = !loading && vocabulary.length > 0;
  const shouldShowLoadingGate = loading || (config && !config.customMode && isEmpty);

  return {
    vocabulary,
    loading,
    error,
    refetch,
    isEmpty,
    canStartGame,
    shouldShowLoadingGate,
    loadingMessage: loading 
      ? 'Loading vocabulary...' 
      : isEmpty 
        ? 'No vocabulary found for selected criteria'
        : ''
  };
}

/**
 * Standalone function to load vocabulary for a given configuration
 * Used for URL parameter auto-loading in game pages
 */
export async function loadVocabulary(config: UnifiedSelectionConfig): Promise<UnifiedVocabularyItem[]> {
  try {
    console.log('🔄 Loading vocabulary for config:', config);

    let query = supabaseBrowser
      .from('centralized_vocabulary')
      .select('*')
      .eq('language', config.language)
      .eq('category', config.categoryId);

    // Add subcategory filter if specified
    if (config.subcategoryId) {
      query = query.eq('subcategory', config.subcategoryId);
    }

    // Add curriculum level filter if needed (for KS4 specific content)
    if (config.curriculumLevel === 'KS4') {
      // For KS4, we might want to filter by specific fields or use different logic
      // For now, we'll use the same query but this can be extended
    }

    const { data, error } = await query.limit(50);

    if (error) {
      console.error('❌ Error loading vocabulary:', error);
      throw error;
    }

    console.log('✅ Vocabulary loaded:', { count: data?.length, data: data?.slice(0, 3) });
    return data || [];
  } catch (error) {
    console.error('❌ Failed to load vocabulary:', error);
    return [];
  }
}

/**
 * Helper function to validate vocabulary before starting a game
 */
export function validateVocabularyForGame(
  vocabulary: UnifiedVocabularyItem[],
  minRequired: number = 1
): { isValid: boolean; message?: string } {
  if (vocabulary.length === 0) {
    return {
      isValid: false,
      message: 'Please wait for vocabulary to load before starting the game.'
    };
  }

  if (vocabulary.length < minRequired) {
    return {
      isValid: false,
      message: `At least ${minRequired} vocabulary items are required to start the game.`
    };
  }

  return { isValid: true };
}
